/*
 * File: app/view/ProtocolPnl.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ProtocolPnl', {
    extend: 'Ext.panel.Panel',
    alias: 'widget.ProtocolPnl',

    requires: [
        'Ext.form.Panel',
        'Ext.button.Button',
        'Ext.form.FieldSet',
        'Ext.form.field.ComboBox',
        'Ext.form.field.Date',
        'Ext.form.field.Number',
        'Ext.form.field.Checkbox',
        'Ext.grid.Panel',
        'Ext.grid.View',
        'Ext.grid.column.Action',
        'Ext.grid.column.Date',
        'Ext.toolbar.Paging',
        'Ext.menu.Menu',
        'Ext.menu.Separator',
        'Ext.toolbar.Separator',
        'Ext.toolbar.Spacer'
    ],

    border: false,
    hidden: true,
    id: 'ProtocolPnl',
    itemId: 'ProtocolPnl',
    layout: 'border',
    header: false,
    title: 'Protocollo',
    titleAlign: 'center',

    initComponent: function () {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'panel',
                    region: 'west',
                    split: true,
                    id: 'ProtocolLeftPnl',
                    itemId: 'ProtocolLeftPnl',
                    width: 225,
                    autoScroll: true,
                    layout: 'fit',
                    collapseDirection: 'left',
                    collapsible: true,
                    iconCls: 'icon-find',
                    title: 'Filtri',
                    titleCollapse: true,
                    items: [
                        {
                            xtype: 'form',
                            applyFilter: function () {
                                if (mc2ui.app.protocolFilterEventTimeoutId > 0) {
                                    clearTimeout(mc2ui.app.protocolFilterEventTimeoutId);
                                }

                                mc2ui.app.protocolFilterEventTimeoutId = setTimeout(function () {
                                    Ext.getStore('ProtocolProtocols').loadPage(1);
                                }, 1000);
                            },
                            border: false,
                            id: 'ProtocolFilterForm',
                            itemId: 'ProtocolFilterForm',
                            autoScroll: true,
                            bodyCls: 'bck-content',
                            bodyPadding: 10,
                            header: false,
                            layout: {
                                type: 'vbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function (button, e) {
                                        var form = Ext.getCmp('ProtocolFilterForm').getForm(),
                                            ds = Ext.getCmp('ProtocolFilterDateStart'),
                                            de = Ext.getCmp('ProtocolFilterDateEnd'),
                                            cd = Ext.getCmp('ProtocolFilterDirection'),
                                            ct = Ext.getCmp('ProtocolFilterType'),
                                            cs = Ext.getCmp('ProtocolFilterSubjectKind'),
                                            cc = Ext.getCmp('ProtocolFilterCorrespondent'),
                                            cm = Ext.getCmp('ProtocolFilterSendMethod');

                                        form.reset();
                                        ds.setValue(new Date((new Date()).getFullYear(), 0, 1, 0, 0, 0, 0));
                                        de.setValue(new Date((new Date()).getFullYear(), 11, 31, 23, 59, 59, 0));
                                        ct.select(0);
                                        cs.select(0);
                                        cc.select(0);
                                        cm.select(0);
                                        cd.select('A');
                                    },
                                    margin: '0 0 10 0',
                                    iconCls: 'icon-arrow_undo',
                                    text: 'Reimposta'
                                },
                                {
                                    xtype: 'fieldset',
                                    title: 'Direzione',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'combobox',
                                            id: 'ProtocolFilterDirection',
                                            itemId: 'ProtocolFilterDirection',
                                            name: 'direction',
                                            allowBlank: false,
                                            allowOnlyWhitespace: false,
                                            editable: false,
                                            forceSelection: true,
                                            queryMode: 'local',
                                            store: 'ProtocolDirectionsFilter',
                                            valueField: 'id',
                                            listeners: {
                                                change: {
                                                    fn: me.onProtocolFilterDirectionChange,
                                                    buffer: 1000,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    title: 'Codice meccanografico',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'combobox',
                                            id: 'ProtocolFilterMechanCode',
                                            itemId: 'ProtocolFilterMechanCode',
                                            name: 'mechan_code',
                                            emptyText: 'Tutti',
                                            displayField: 'mechan_code',
                                            forceSelection: false,
                                            allowBlank: true,
                                            store: 'ProtocolMechanCodes',
                                            valueField: 'mechan_code',
                                            triggers: {
                                                clear: {
                                                    cls: 'x-form-clear-trigger',
                                                    handler: function() {
                                                        this.setValue('');
                                                    }
                                                }
                                            },
                                            listeners: {
                                                change: {
                                                    fn: function () {
                                                        Ext.getCmp('ProtocolFilterForm').applyFilter();
                                                    },
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    title: 'Periodo',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'datefield',
                                            endDateField: 'ProtocolFilterDateEnd',
                                            id: 'ProtocolFilterDateStart',
                                            itemId: 'ProtocolFilterDateStart',
                                            fieldLabel: 'Dal',
                                            labelAlign: 'right',
                                            labelWidth: 50,
                                            name: 'date_start',
                                            vtype: 'daterange',
                                            editable: false,
                                            format: 'd/m/Y',
                                            startDay: 1,
                                            submitFormat: 'c',
                                            listeners: {
                                                change: {
                                                    fn: me.onProtocolFilterDateStartChange,
                                                    buffer: 1000,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'datefield',
                                            startDateField: 'ProtocolFilterDateStart',
                                            id: 'ProtocolFilterDateEnd',
                                            itemId: 'ProtocolFilterDateEnd',
                                            fieldLabel: 'al',
                                            labelAlign: 'right',
                                            labelWidth: 50,
                                            name: 'date_end',
                                            vtype: 'daterange',
                                            editable: false,
                                            format: 'd/m/Y',
                                            startDay: 1,
                                            submitFormat: 'c',
                                            listeners: {
                                                change: {
                                                    fn: me.onProtocolFilterDateEndChange,
                                                    buffer: 1000,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    title: 'Voce Titolario',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'combobox',
                                            id: 'ProtocolFilterType',
                                            itemId: 'ProtocolFilterType',
                                            name: 'type',
                                            emptyText: 'Tutte',
                                            hideTrigger: true,
                                            matchFieldWidth: false,
                                            anyMatch: true,
                                            autoSelect: false,
                                            displayField: 'full_denomination',
                                            forceSelection: true,
                                            queryMode: 'local',
                                            store: 'ProtocolTypesFilter',
                                            typeAhead: true,
                                            valueField: 'id',
                                            listeners: {
                                                change: {
                                                    fn: me.onProtocolFilterTypeChange,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    title: 'Tipo Oggetto',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'combobox',
                                            id: 'ProtocolFilterSubjectKind',
                                            itemId: 'ProtocolFilterSubjectKind',
                                            name: 'subject_kind',
                                            emptyText: 'Tutti',
                                            hideTrigger: true,
                                            matchFieldWidth: false,
                                            anyMatch: true,
                                            autoSelect: false,
                                            displayField: 'title',
                                            forceSelection: true,
                                            queryMode: 'local',
                                            store: 'ProtocolSubjectKindsFilter',
                                            typeAhead: true,
                                            valueField: 'id',
                                            listeners: {
                                                change: {
                                                    fn: me.onProtocolFilterSubjectKindChange,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    title: 'Mittente / Destinatario',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'combobox',
                                            id: 'ProtocolFilterCorrespondent',
                                            itemId: 'ProtocolFilterCorrespondent',
                                            name: 'correspondent',
                                            emptyText: 'Tutti',
                                            hideTrigger: true,
                                            matchFieldWidth: false,
                                            anyMatch: true,
                                            autoSelect: false,
                                            displayField: 'title',
                                            forceSelection: true,
                                            queryMode: 'local',
                                            store: 'ProtocolCorrespondentsFilter',
                                            typeAhead: true,
                                            valueField: 'id',
                                            listeners: {
                                                change: {
                                                    fn: me.onProtocolFilterCorrespondentChange,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    title: 'Numero',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'numberfield',
                                            id: 'ProtocolFilterNumberStart',
                                            itemId: 'ProtocolFilterNumberStart',
                                            fieldLabel: 'Dal',
                                            labelAlign: 'right',
                                            labelWidth: 50,
                                            name: 'number_start',
                                            hideTrigger: true,
                                            allowDecimals: false,
                                            allowExponential: false,
                                            minValue: 0,
                                            listeners: {
                                                change: {
                                                    fn: me.onArchiveFilterMetadataNumericStartChange11,
                                                    buffer: 1000,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'numberfield',
                                            id: 'ProtocolFilterNumberEnd',
                                            itemId: 'ProtocolFilterNumberEnd',
                                            fieldLabel: 'al',
                                            labelAlign: 'right',
                                            labelWidth: 50,
                                            name: 'number_end',
                                            hideTrigger: true,
                                            allowDecimals: false,
                                            allowExponential: false,
                                            minValue: 0,
                                            listeners: {
                                                change: {
                                                    fn: me.onArchiveFilterMetadataNumericEndChange11,
                                                    buffer: 1000,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    title: 'Oggetto',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'textfield',
                                            id: 'ProtocolFilterDescription',
                                            itemId: 'ProtocolFilterDescription',
                                            name: 'description',
                                            listeners: {
                                                change: {
                                                    fn: me.onProtocolFilterDescriptionChange,
                                                    buffer: 1000,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    title: 'Dettagli',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'checkboxfield',
                                            flex: 1,
                                            id: 'ProtocolFilterDocument',
                                            itemId: 'ProtocolFilterDocument',
                                            name: 'document',
                                            boxLabel: 'Con Allegati',
                                            uncheckedValue: 'off',
                                            listeners: {
                                                change: {
                                                    fn: me.onProtocolFilterDocumentChange,
                                                    buffer: 1000,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'checkboxfield',
                                            flex: 1,
                                            id: 'ProtocolFilterSibling',
                                            itemId: 'ProtocolFilterSibling',
                                            name: 'sibling',
                                            boxLabel: 'Con Collegamenti',
                                            uncheckedValue: 'off',
                                            listeners: {
                                                change: {
                                                    fn: me.onProtocolFilterSiblingChange,
                                                    buffer: 1000,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    title: 'Riservato',
                                    layout: {
                                        type: 'hbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'checkboxfield',
                                            flex: 1,
                                            name: 'reserved',
                                            boxLabel: 'Si',
                                            inputValue: '1',
                                            uncheckedValue: '0',
                                            checked: true,
                                            listeners: {
                                                change: {
                                                    fn: () => {
                                                        Ext.getCmp('ProtocolFilterForm').applyFilter();
                                                    },
                                                    buffer: 1000,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'checkboxfield',
                                            flex: 1,
                                            name: 'not_reserved',
                                            boxLabel: 'No',
                                            inputValue: '1',
                                            uncheckedValue: '0',
                                            checked: true,
                                            listeners: {
                                                change: {
                                                    fn: () => {
                                                        Ext.getCmp('ProtocolFilterForm').applyFilter();
                                                    },
                                                    buffer: 1000,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    title: 'Mezzo di Invio',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'combobox',
                                            id: 'ProtocolFilterSendMethod',
                                            itemId: 'ProtocolFilterSendMethod',
                                            name: 'send_method',
                                            editable: false,
                                            matchFieldWidth: false,
                                            displayField: 'title',
                                            forceSelection: true,
                                            queryMode: 'local',
                                            store: 'ProtocolSendMethodsFilter',
                                            valueField: 'id',
                                            listeners: {
                                                change: {
                                                    fn: me.onProtocolFilterSendMethodChange,
                                                    buffer: 1000,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    title: 'Note',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'textfield',
                                            id: 'ProtocolFilterNote',
                                            itemId: 'ProtocolFilterNote',
                                            name: 'note',
                                            listeners: {
                                                change: {
                                                    fn: me.onProtocolFilterNoteChange,
                                                    buffer: 1000,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    title: 'Atto',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'textfield',
                                            id: 'ProtocolFilterAct',
                                            itemId: 'ProtocolFilterAct',
                                            name: 'act',
                                            listeners: {
                                                change: {
                                                    fn: me.onProtocolFilterActChange,
                                                    buffer: 1000,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    title: 'Fascicolo',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'textfield',
                                            id: 'ProtocolFilterDossier',
                                            itemId: 'ProtocolFilterDossier',
                                            name: 'dossier',
                                            listeners: {
                                                change: {
                                                    fn: me.onProtocolFilterDossierChange,
                                                    buffer: 1000,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                },
                {
                    xtype: 'panel',
                    permissible: true,
                    region: 'center',
                    split: true,
                    id: 'ProtocolMainPnl',
                    itemId: 'ProtocolMainPnl',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'gridpanel',
                            flex: 1,
                            border: false,
                            id: 'ProtocolsGrid',
                            itemId: 'ProtocolsGrid',
                            emptyText: 'Nessun protocollo trovato.',
                            enableColumnMove: false,
                            store: 'ProtocolProtocols',
                            viewConfig: {
                                getRowClass: function (record, rowIndex, rowParams, store) {
                                    if (record.get('canceled')) {
                                        return 'cancelled-protocol-row';
                                    }
                                },
                                id: 'ProtocolsGridView',
                                itemId: 'ProtocolsGridView'
                            },
                            columns: [
                                {
                                    xtype: 'actioncolumn',
                                    width: 20,
                                    resizable: false,
                                    align: 'center',
                                    hideable: false,
                                    stopSelection: false,
                                    items: [
                                        {
                                            getClass: function (v, metadata, r, rowIndex, colIndex, store) {
                                                var res = r.get('reserved'),
                                                    can = r.get('canceled');

                                                if (res && !can) {
                                                    return 'icon-lock';
                                                } else if (res && can) {
                                                    return 'icon-lock_delete';
                                                } else if (!res && can) {
                                                    return 'icon-delete';
                                                }
                                            },
                                            getTip: function (v, metadata, r, rowIndex, colIndex, store) {
                                                var res = r.get('reserved'),
                                                    can = r.get('canceled');

                                                if (res && !can) {
                                                    return 'Riservato';
                                                } else if (res && can) {
                                                    return 'Riservato e Annullato';
                                                } else if (!res && can) {
                                                    return 'Annullato';
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'gridcolumn',
                                    width: 65,
                                    resizable: false,
                                    sortable: true,
                                    align: 'right',
                                    dataIndex: 'protocol_number',
                                    hideable: false,
                                    text: 'Numero'
                                },
                                {
                                    xtype: 'datecolumn',
                                    width: 80,
                                    resizable: false,
                                    sortable: true,
                                    align: 'center',
                                    dataIndex: 'date',
                                    hideable: false,
                                    text: 'Data',
                                    format: 'd/m/Y'
                                },
                                {
                                    xtype: 'gridcolumn',
                                    width: 80,
                                    resizable: false,
                                    sortable: true,
                                    dataIndex: 'type_text',
                                    text: 'Titolario'
                                },
                                {
                                    xtype: 'gridcolumn',
                                    width: 70,
                                    resizable: false,
                                    sortable: true,
                                    align: 'center',
                                    dataIndex: 'direction_text',
                                    text: 'Direzione'
                                },
                                {
                                    xtype: 'gridcolumn',
                                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                        if (value) {
                                            metaData.tdAttr = 'data-qtip="' + value + '"';
                                        }
                                        return value;
                                    },
                                    resizable: false,
                                    sortable: true,
                                    dataIndex: 'description',
                                    hideable: false,
                                    text: 'Oggetto',
                                    flex: 1
                                },
                                {
                                    xtype: 'gridcolumn',
                                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                        if (value) {
                                            metaData.tdAttr = 'data-qtip="' + value + '"';
                                        }
                                        return value;
                                    },
                                    resizable: false,
                                    sortable: true,
                                    dataIndex: 'correspondents_text',
                                    text: 'Mitt. / Dest.',
                                    flex: 1
                                },
                                {
                                    xtype: 'gridcolumn',
                                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                        if (value) {
                                            metaData.tdAttr = 'data-qtip="' + value + '"';
                                        }
                                        return value;
                                    },
                                    width: 80,
                                    sortable: true,
                                    dataIndex: 'send_method_text',
                                    text: 'Mezzo'
                                },
                                {
                                    xtype: 'gridcolumn',
                                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                        if (value) {
                                            metaData.tdAttr = 'data-qtip="' + value + '"';
                                        }
                                        return value;
                                    },
                                    sortable: true,
                                    dataIndex: 'subject_kind_text',
                                    text: 'Tipo'
                                },
                                {
                                    xtype: 'gridcolumn',
                                    sortable: true,
                                    dataIndex: 'external_act_number',
                                    text: 'Atto'
                                },
                                {
                                    xtype: 'gridcolumn',
                                    sortable: true,
                                    dataIndex: 'dossier',
                                    text: 'Fascicolo'
                                },
                                {
                                    xtype: 'actioncolumn',
                                    width: 90,
                                    resizable: false,
                                    align: 'center',
                                    hideable: false,
                                    stopSelection: false,
                                    items: [
                                        {
                                            getClass: function (v, metadata, r, rowIndex, colIndex, store) {
                                                if (r.get('count_protocols') > 0 && !r.get('reserved')) {
                                                    return 'icon-link';
                                                }
                                            },
                                            getTip: function (v, metadata, r, rowIndex, colIndex, store) {
                                                var protocolsCnt = r.get('count_protocols');

                                                if (protocolsCnt > 0 && !r.get('canceled')) {
                                                    var io = 'o';
                                                    if (protocolsCnt > 1) {
                                                        io = 'i';
                                                    }
                                                    return protocolsCnt + ' protocoll' + io + ' collegat' + io;
                                                }
                                            },
                                            handler: function (view, rowIndex, colIndex, item, e, record, row) {
                                                var protocolsCnt = record.get('count_protocols');

                                                if (protocolsCnt > 0 && !record.get('canceled') && !record.get('reserved')) {
                                                    Ext.widget('ProtocolLinkedProtocolsWin').show();
                                                    Ext.getCmp('ProtocolLinkedProtocolsWin').setTitle('Protocolli collegati al protocollo n. ' + record.get('id'));
                                                    Ext.getStore('ProtocolLinkedProtocols').load({
                                                        params: {
                                                            protocol: record.get('id')
                                                        }
                                                    });
                                                }
                                            }
                                        },
                                        {
                                            getClass: function (v, metadata, r, rowIndex, colIndex, store) {
                                                if (r.get('count_documents') > 0 && (!r.get('reserved') || mc2ui.app.settings.canManageReservedProtocol === true)) {
                                                    return 'icon-attach';
                                                }
                                            },
                                            getTip: function (v, metadata, r, rowIndex, colIndex, store) {
                                                var documentsCnt = r.get('count_documents');

                                                if (documentsCnt > 0 && !r.get('canceled')) {
                                                    var io = 'o';
                                                    if (documentsCnt > 1) {
                                                        io = 'i';
                                                    }
                                                    return documentsCnt + ' document' + io + ' allegat' + io;
                                                }
                                            },
                                            handler: function (view, rowIndex, colIndex, item, e, record, row) {
                                                var documentsCnt = record.get('count_documents');

                                                if (documentsCnt > 0 && !record.get('canceled')
                                                    && (!record.get('reserved') || mc2ui.app.settings.canManageReservedProtocol === true
                                                    )) {
                                                    Ext.widget('ProtocolLinkedDocumentsWin').show();
                                                    Ext.getCmp('ProtocolLinkedDocumentsWin').setTitle('Documenti allegati al protocollo n. ' + record.get('protocol_number'));
                                                    Ext.getStore('ProtocolLinkedDocuments').load({
                                                        params: {
                                                            protocol: record.get('id')
                                                        }
                                                    });
                                                }
                                            }
                                        },
                                        {
                                            handler: function (view, rowIndex, colIndex, item, e, record, row) {
                                                var msg = '';

                                                if (!record.get('reserved')) {
                                                    msg = '<div style="display: table">' +
                                                        '<div style="display: table-row">' +
                                                        '<div style="display: table-cell"><b>Oggetto:&nbsp;</b></div>' +
                                                        '<div style="display: table-cell">' + record.get('description') + '</div>' +
                                                        '</div>' +
                                                        '<div style="display: table-row">' +
                                                        '<div style="display: table-cell"><b>Corrispondenti:&nbsp;</b></div>' +
                                                        '<div style="display: table-cell">' + record.get('correspondents_text') + '</div>' +
                                                        '</div>' +
                                                        '<div style="display: table-row">' +
                                                        '<div style="display: table-cell"><b>Atto:&nbsp;</b></div>' +
                                                        '<div style="display: table-cell">' + record.get('external_act_number') + '</div>' +
                                                        '</div>' +
                                                        '<div style="display: table-row">' +
                                                        '<div style="display: table-cell"><b>Fascicolo:&nbsp;</b></div>' +
                                                        '<div style="display: table-cell">' + record.get('dossier') + '</div>' +
                                                        '</div>' +
                                                        '<div style="display: table-row">' +
                                                        '<div style="display: table-cell"><b>Note:&nbsp;</b></div>' +
                                                        '<div style="display: table-cell">' + record.get('note') + '</div>' +
                                                        '</div>' +
                                                        '</div>';
                                                } else {
                                                    msg = 'Protocollo Riservato';
                                                }
                                                Ext.Msg.alert('Dettagli protocollo n. ' + record.get('protocol_number'), msg);
                                            },
                                            iconCls: 'icon-information',
                                            tooltip: 'Dettagli'
                                        },
                                        {
                                            getClass: function (v, metadata, r, rowIndex, colIndex, store) {
                                                if (r.get('mail_sending')) {
                                                    return 'icon-email_go';
                                                }
                                            },
                                            tooltip: 'Invio mail in corso ...'
                                        }
                                    ]
                                }
                            ],
                            dockedItems: [
                                {
                                    xtype: 'toolbar',
                                    dock: 'top',
                                    items: [
                                        {
                                            xtype: 'button',
                                            handler: function (button, e) {
                                                Ext.widget('ProtocolProtocolNewWin').show();
                                                Ext.getCmp('ProtocolProtocolNewWin').record = Ext.getCmp('ProtocolsGrid').getSelectionModel().getSelection()[0];
                                                Ext.getCmp('ProtocolNewDate').setValue(new Date());
                                                Ext.getStore('ProtocolLinkedCorrespondentsForm').removeAll();
                                                Ext.getStore('ProtocolLinkedDocumentsForm').removeAll();
                                                Ext.getStore('ProtocolLinkedProtocolsForm').removeAll();
                                            },
                                            id: 'ProtocolNewBtn',
                                            itemId: 'ProtocolNewBtn',
                                            iconCls: 'icon-add',
                                            text: 'Nuovo Protocollo'
                                        }
                                    ]
                                },
                                {
                                    xtype: 'pagingtoolbar',
                                    dock: 'bottom',
                                    displayInfo: true,
                                    displayMsg: 'Protocolli {0} - {1} di {2}',
                                    emptyMsg: 'Nessun protocollo',
                                    store: 'ProtocolProtocols'
                                }
                            ],
                            listeners: {
                                itemcontextmenu: {
                                    fn: me.onProtocolsGridItemContextMenu,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'menu',
                            permissible: true,
                            hidden: true,
                            id: 'ProtocolProtocolEditMn',
                            itemId: 'ProtocolProtocolEditMn',
                            items: [
                                {
                                    xtype: 'menuitem',
                                    handler: function (item, e) {
                                        var record = Ext.getCmp('ProtocolProtocolEditMn').record,
                                            /*
                                            docs = [],
                                            corrs = [],
                                            prots = [],
                                            storeD = Ext.getStore('ProtocolDocuments'),
                                            storeC = Ext.getStore('ProtocolCorrespondents'),
                                            storeP = Ext.getStore('ProtocolProtocols'),*/
                                            storeDocs = Ext.getStore('ProtocolLinkedDocumentsForm'),
                                            storeCorrs = Ext.getStore('ProtocolLinkedCorrespondentsForm'),
                                            storeProts = Ext.getStore('ProtocolLinkedProtocolsForm');

                                        Ext.widget('ProtocolProtocolNewWin').show();
                                        Ext.getCmp('ProtocolProtocolNewWin').record = record;
                                        Ext.getCmp('ProtocolNewForm').loadRecord(record);

                                        if (record.get('locked_docs') === true) {
                                            Ext.getCmp('ProtocolNewLinkedDocumentsGrid').getView().disable();
                                            Ext.getCmp('ProtocolDocumentToolBar').disable();
                                        }

                                        storeDocs.removeAll();
                                        storeCorrs.removeAll();
                                        storeProts.removeAll();

                                        storeDocs.load({
                                            params: {
                                                protocol: record.get('id')
                                            }
                                        });

                                        storeCorrs.load({
                                            params: {
                                                protocol: record.get('id')
                                            }
                                        });

                                        storeProts.load({
                                            params: {
                                                protocol: record.get('id')
                                            }
                                        });

                                        Ext.getCmp('ProtocolReserverdCheck').setValue(record.get('reserved'));
                                    },
                                    id: 'contextProtocolProtocolEdit',
                                    itemId: 'contextProtocolProtocolEdit',
                                    iconCls: 'icon-pencil',
                                    text: 'Modifica'
                                },
                                {
                                    xtype: 'menuitem',
                                    handler: function (item, e) {
                                        var record = Ext.getCmp('ProtocolsGrid').getSelectionModel().getSelection()[0],
                                            storeDocs = Ext.getStore('ProtocolLinkedDocumentsForm'),
                                            storeProts = Ext.getStore('ProtocolLinkedProtocolsForm');

                                        Ext.Msg.show({
                                            title: 'Protocollo n. ' + record.get('protocol_number'),
                                            msg: 'Sei sicuro di voler annullare questo Protocollo?',
                                            buttons: Ext.Msg.YESNO,
                                            fn: function (r) {
                                                if (r == 'yes') {
                                                    store = Ext.getStore('ProtocolProtocols');
                                                    record.set('canceled', true);
                                                    store.sync({
                                                        callback: function () {
                                                            store.load();
                                                        },
                                                        success: function () {
                                                            Ext.Msg.show({
                                                                title: 'Successo',
                                                                msg: 'Protocollo annullato.<br />Creare un nuovo protocollo sulla base di quello annullato?',
                                                                buttons: Ext.Msg.YESNO,
                                                                fn: function (r) {
                                                                    if (r == 'yes') {
                                                                        var protocol_id = record.get('id'),
                                                                            storeDocs = Ext.getStore('ProtocolLinkedDocumentsForm'),
                                                                            storeCorrs = Ext.getStore('ProtocolLinkedCorrespondentsForm'),
                                                                            storeProts = Ext.getStore('ProtocolLinkedProtocolsForm');

                                                                        delete record.data.id;
                                                                        delete record.data.protocol_number;
                                                                        delete record.data.date;

                                                                        Ext.widget('ProtocolProtocolNewWin').show();

                                                                        Ext.getCmp('ProtocolProtocolNewWin').record = record;
                                                                        Ext.getCmp('ProtocolNewForm').loadRecord(record);
                                                                        Ext.getCmp('ProtocolNewDate').setValue(new Date());
                                                                        Ext.getCmp('ProtocolReserverdCheck').setValue(record.get('reserved'));
                                                                        if (record.get('locked_docs') === true) {
                                                                            Ext.getCmp('ProtocolNewLinkedDocumentsGrid').getView().disable();
                                                                            Ext.getCmp('ProtocolDocumentToolBar').disable();
                                                                        }

                                                                        storeDocs.removeAll();
                                                                        storeCorrs.removeAll();
                                                                        storeProts.removeAll();

                                                                        // Aggiungere i documenti al nuovo protocollo prendendoli da un altro (il precedente in questo caso)
                                                                        // comporta gestione lato server per evitare la doppia stampigliatura
                                                                        /*storeDocs.load({
                                                                            params: {
                                                                                protocol: protocol_id
                                                                            }
                                                                        });

                                                                        storeCorrs.load({
                                                                            params: {
                                                                                protocol: protocol_id
                                                                            }
                                                                        });

                                                                        storeProts.load({
                                                                            params: {
                                                                                protocol: protocol_id
                                                                            }
                                                                        });
                                                                        */

                                                                    }
                                                                }
                                                            });
                                                        },
                                                        failure: function () {
                                                            Ext.Msg.alert('Attenzione', 'Protocollo NON annullato');
                                                        }
                                                    });
                                                }
                                            }
                                        });
                                    },
                                    id: 'contextProtocolProtocolCancel',
                                    itemId: 'contextProtocolProtocolCancel',
                                    iconCls: 'icon-delete',
                                    text: 'Annulla'
                                },
                                {
                                    xtype: 'menuseparator'
                                },
                                {
                                    xtype: 'menuitem',
                                    handler: function (item, e) {
                                        var record = Ext.getCmp('ProtocolsGrid').getSelectionModel().getSelection()[0];

                                        Ext.widget('ProtocolProtocolHistoryWin').show();
                                        Ext.getCmp('ProtocolProtocolHistoryWin').setTitle('Storico protocollo n. ' + record.get('id'));

                                        Ext.getStore('ProtocolHistories').load({
                                            params: {
                                                protocol: record.get('id')
                                            }
                                        });
                                    },
                                    id: 'contextProtocolProtocolHistory',
                                    itemId: 'contextProtocolProtocolHistory',
                                    iconCls: 'icon-hourglass',
                                    text: 'Storico modifiche'
                                },
                                {
                                    xtype: 'menuseparator'
                                },
                                {
                                    xtype: 'menuitem',
                                    handler: function (item, e) {
                                        var record = Ext.getCmp('ProtocolsGrid').getSelectionModel().getSelection()[0];

                                        Ext.Msg.show({
                                            title: 'Protocollo n. ' + record.get('protocol_number'),
                                            msg: 'Sei sicuro di voler eliminare questo Protocollo?',
                                            buttons: Ext.Msg.YESNO,
                                            fn: function (r) {
                                                if (r == 'yes') {
                                                    store = Ext.getStore('ProtocolProtocols');
                                                    store.remove(record);
                                                    store.sync({
                                                        callback: function () {
                                                            store.load();
                                                            Ext.getStore('ProtocolTypesTree').load();
                                                        },
                                                        success: function () {
                                                            Ext.Msg.alert('Successo', 'Protocollo eliminato');
                                                        },
                                                        failure: function () {
                                                            Ext.Msg.alert('Attenzione', 'Protocollo NON eliminato');
                                                        }
                                                    });
                                                }
                                            }
                                        });
                                    },
                                    id: 'contextProtocolProtocolDelete',
                                    itemId: 'contextProtocolProtocolDelete',
                                    iconCls: 'icon-cancel',
                                    text: 'Elimina',
                                    hidden: true
                                },
                                {
                                    xtype: 'menuseparator',
                                    hidden: true
                                },
                                {
                                    xtype: 'menuitem',
                                    id: 'contextProtocolProtocolPrints',
                                    itemId: 'contextProtocolProtocolPrints',
                                    iconCls: 'icon-printer',
                                    text: 'Stampe singole',
                                    menu: {
                                        xtype: 'menu',
                                        items: [
                                            {
                                                xtype: 'menuitem',
                                                handler: function (item, e) {
                                                    var rec = {},
                                                        protocol = Ext.getCmp('ProtocolsGrid').getSelectionModel().getSelection()[0];

                                                    rec.newSpool = 1;
                                                    rec.print = 'Protocol';
                                                    rec.namespace = 'Protocol';
                                                    rec.type = 'PDF';
                                                    rec.mime = 'application/pdf';
                                                    rec.protocol = protocol.get('id');
                                                    rec.protocol_number = protocol.get('protocol_number');
                                                    rec.date = protocol.get('date');

                                                    Ext.Ajax.request({
                                                        url: '/mc2-api/core/print',
                                                        params: rec,
                                                        success: function (response, opts) {
                                                            var res = Ext.decode(response.responseText);
                                                            mc2ui.app.showNotifyPrint(res);
                                                        }
                                                    });
                                                },
                                                iconCls: 'icon-information',
                                                text: 'Protocollo'
                                            },
                                            {
                                                xtype: 'menuseparator'
                                            },
                                            {
                                                xtype: 'menuitem',
                                                handler: function (item, e) {
                                                    var rec = {},
                                                        protocol = Ext.getCmp('ProtocolsGrid').getSelectionModel().getSelection()[0];

                                                    rec.newSpool = 1;
                                                    rec.print = 'Label';
                                                    rec.namespace = 'Protocol';
                                                    rec.type = 'PDF';
                                                    rec.mime = 'application/pdf';
                                                    rec.protocol = protocol.get('id');
                                                    rec.protocol_number = protocol.get('protocol_number');
                                                    rec.date = protocol.get('date');

                                                    Ext.Ajax.request({
                                                        url: '/mc2-api/core/print',
                                                        params: rec,
                                                        success: function (response, opts) {
                                                            var res = Ext.decode(response.responseText);
                                                            mc2ui.app.showNotifyPrint(res);
                                                        }
                                                    });
                                                },
                                                iconCls: 'icon-tag_orange',
                                                text: 'Etichetta'
                                            },
                                            {
                                                xtype: 'menuitem',
                                                handler: function (item, e) {
                                                    Ext.widget('ProtocolPrintsBarcodeWin').show();
                                                    Ext.getCmp('ProtocolPrintsBarcodeWin').single = true;
                                                },
                                                iconCls: 'icon-text_direction',
                                                text: 'Codice a barre'
                                            }
                                        ]
                                    }
                                },
                                {
                                    xtype: 'menuitem',
                                    handler: function (item, e) {
                                        var sel = Ext.getCmp('ProtocolsGrid').getSelectionModel().getSelection()[0],
                                            data = {
                                                subject: sel.get('short_description'),
                                                protocol: sel.get('id')
                                            };

                                        Ext.widget('ArchiveMailSendWin').show();
                                        Ext.getStore('ArchiveDocumentFiles').removeAll();
                                        Ext.getStore('ProtocolLinkedDocuments').load({
                                            params: {
                                                protocol: sel.get('id')
                                            },
                                            callback: function (a, r) {
                                                var res = Ext.decode(r.response.responseText);
                                                if (res.success === true) {
                                                    Ext.getStore('ArchiveDocumentFiles').add(res.results);
                                                }
                                            }
                                        });



                                        Ext.getCmp('SendMailFrm').getForm().setValues(data);

                                    },
                                    iconCls: 'icon-mail',
                                    text: 'Invia mail'
                                }
                            ]
                        }
                    ]
                }
            ],
            dockedItems: [
                {
                    xtype: 'toolbar',
                    permissible: true,
                    dock: 'top',
                    id: 'ProtocolToolbar',
                    itemId: 'ProtocolToolbar',
                    items: [
                        {
                            xtype: 'button',
                            handler: function (button, e) {
                                Ext.widget('ProtocolTypesWin').show();
                            },
                            id: 'ProtocolTypesBtn',
                            itemId: 'ProtocolTypesBtn',
                            iconCls: 'icon-book_tabs',
                            text: 'Titolario'
                        },
                        {
                            xtype: 'button',
                            handler: function (button, e) {
                                Ext.widget('ProtocolCorrespondentsWin').show();
                                Ext.getStore('ProtocolCorrespondents').load();
                            },
                            id: 'ProtocolCorrespondentsBtn',
                            itemId: 'ProtocolCorrespondentsBtn',
                            iconCls: 'icon-group_go',
                            text: 'Mittenti / Destinatari'
                        },
                        {
                            xtype: 'button',
                            handler: function (button, e) {
                                Ext.widget('ProtocolSubjectKindsWin').show();
                                Ext.getStore('ProtocolSubjectKinds').load();
                            },
                            id: 'ProtocolSubjectKindsBtn',
                            itemId: 'ProtocolSubjectKindsBtn',
                            iconCls: 'icon-text_list_bullets',
                            text: 'Tipi di oggetto'
                        },
                        {
                            xtype: 'button',
                            handler: function (button, e) {
                                Ext.widget('ProtocolSendMethodsWin').show();
                                Ext.getStore('ProtocolSendMethods').load();
                            },
                            id: 'ProtocolSendMethodsBtn',
                            itemId: 'ProtocolSendMethodsBtn',
                            iconCls: 'icon-email_go',
                            text: 'Mezzi di invio'
                        },
                        {
                            xtype: 'button',
                            handler: function (button, e) {
                                Ext.widget('RegisterWin').show();
                                Ext.getStore('Register').load();
                            },
                            iconCls: 'icon-folder_database',
                            text: 'Registro'
                        },
                        {
                            xtype: 'tbseparator'
                        },
                        {
                            xtype: 'button',
                            handler: function (button, e) {
                                Ext.widget('ProtocolActionsWin').show();
                                Ext.getStore('ProtocolActions').load();
                            },
                            hidden: true,
                            id: 'ProtocolActionsBtn',
                            itemId: 'ProtocolActionsBtn',
                            iconCls: 'icon-connect',
                            text: 'Protocollazione automatica'
                        },
                        {
                            xtype: 'tbseparator',
                            hidden: true
                        },
                        {
                            xtype: 'button',
                            id: 'ProtocolPrintsBtn',
                            itemId: 'ProtocolPrintsBtn',
                            iconCls: 'icon-printer',
                            text: 'Stampe',
                            menu: {
                                xtype: 'menu',
                                items: [
                                    {
                                        xtype: 'menuitem',
                                        handler: function (item, e) {
                                            var rec = {};

                                            rec.newSpool = 1;
                                            rec.print = 'Filtered';
                                            rec.namespace = 'Protocol';
                                            rec.type = 'PDF';
                                            rec.mime = 'application/pdf';
                                            rec.filter = Ext.JSON.encode(Ext.getCmp('ProtocolFilterForm').getForm().getValues());

                                            rec.sort = [];
                                            Ext.each(Ext.getCmp('ProtocolsGrid').getStore().getSorters(), function (sorter) {
                                                rec.sort = rec.sort.concat({
                                                    "property": sorter.property,
                                                    "direction": sorter.direction
                                                });
                                            });
                                            rec.sort = Ext.encode(rec.sort);


                                            Ext.Ajax.request({
                                                url: '/mc2-api/core/print',
                                                params: rec,
                                                success: function (response, opts) {
                                                    var res = Ext.decode(response.responseText);
                                                    mc2ui.app.showNotifyPrint(res);
                                                }
                                            });
                                        },
                                        iconCls: 'icon-find',
                                        text: 'Riepilogo protocolli filtrati'
                                    },
                                    {
                                        xtype: 'menuseparator'
                                    },
                                    {
                                        xtype: 'menuitem',
                                        handler: function (item, e) {
                                            var rec = {};

                                            rec.newSpool = 1;
                                            rec.print = 'Types';
                                            rec.namespace = 'Protocol';
                                            rec.type = 'PDF';
                                            rec.mime = 'application/pdf';

                                            Ext.Ajax.request({
                                                url: '/mc2-api/core/print',
                                                params: rec,
                                                success: function (response, opts) {
                                                    var res = Ext.decode(response.responseText);
                                                    mc2ui.app.showNotifyPrint(res);
                                                }
                                            });
                                        },
                                        iconCls: 'icon-book_tabs',
                                        text: 'Struttura Titolario'
                                    },
                                    {
                                        xtype: 'menuitem',
                                        handler: function (item, e) {
                                            var rec = {};

                                            rec.newSpool = 1;
                                            rec.print = 'Correspondents';
                                            rec.namespace = 'Protocol';
                                            rec.type = 'PDF';
                                            rec.mime = 'application/pdf';

                                            Ext.Ajax.request({
                                                url: '/mc2-api/core/print',
                                                params: rec,
                                                success: function (response, opts) {
                                                    var res = Ext.decode(response.responseText);
                                                    mc2ui.app.showNotifyPrint(res);
                                                }
                                            });
                                        },
                                        iconCls: 'icon-group_go',
                                        text: 'Lista Corrispondenti'
                                    },
                                    {
                                        xtype: 'menuseparator'
                                    },
                                    {
                                        xtype: 'menuitem',
                                        handler: function (item, e) {
                                            var rec = {};

                                            rec.newSpool = 1;
                                            rec.print = 'Labels';
                                            rec.namespace = 'Protocol';
                                            rec.type = 'PDF';
                                            rec.mime = 'application/pdf';
                                            rec.filter = Ext.JSON.encode(Ext.getCmp('ProtocolFilterForm').getForm().getValues());

                                            Ext.Ajax.request({
                                                url: '/mc2-api/core/print',
                                                params: rec,
                                                success: function (response, opts) {
                                                    var res = Ext.decode(response.responseText);
                                                    mc2ui.app.showNotifyPrint(res);
                                                }
                                            });
                                        },
                                        iconCls: 'icon-tag_orange',
                                        text: 'Etichette'
                                    },
                                    {
                                        xtype: 'menuitem',
                                        handler: function (item, e) {
                                            Ext.widget('ProtocolPrintsBarcodeWin').show();
                                        },
                                        iconCls: 'icon-text_direction',
                                        text: 'Codici a barre'
                                    }
                                ]
                            }
                        },
                        {
                            xtype: 'tbspacer',
                            flex: 1
                        },

                    ]
                }
            ],
            listeners: {
                boxready: {
                    fn: me.onProtocolPnlBoxReady,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onProtocolFilterDirectionChange: function (field, newValue, oldValue, eOpts) {
        Ext.getCmp('ProtocolFilterForm').applyFilter();
    },

    onProtocolFilterDateStartChange: function (field, newValue, oldValue, eOpts) {
        Ext.getCmp('ProtocolFilterForm').applyFilter();
    },

    onProtocolFilterDateEndChange: function (field, newValue, oldValue, eOpts) {
        Ext.getCmp('ProtocolFilterForm').applyFilter();
    },

    onProtocolFilterTypeChange: function (field, newValue, oldValue, eOpts) {
        if (parseInt(Ext.getCmp('ProtocolFilterType').getValue()) > 0) {
            Ext.getCmp('ProtocolFilterForm').applyFilter();
        } else if (!newValue) {
            Ext.getCmp('ProtocolFilterType').setValue();
            Ext.getCmp('ProtocolFilterForm').applyFilter();
        } else if (oldValue) {
            if (newValue.length < 2 && newValue.length < oldValue.length) {
                Ext.getCmp('ProtocolFilterType').setValue();
                Ext.getCmp('ProtocolFilterForm').applyFilter();
            }
        }
    },

    onProtocolFilterSubjectKindChange: function (field, newValue, oldValue, eOpts) {
        if (parseInt(Ext.getCmp('ProtocolFilterSubjectKind').getValue()) > 0) {
            Ext.getCmp('ProtocolFilterForm').applyFilter();
        } else if (!newValue) {
            Ext.getCmp('ProtocolFilterSubjectKind').setValue();
            Ext.getCmp('ProtocolFilterForm').applyFilter();
        } else if (oldValue) {
            if (newValue.length < 2 && newValue.length < oldValue.length) {
                Ext.getCmp('ProtocolFilterSubjectKind').setValue();
                Ext.getCmp('ProtocolFilterForm').applyFilter();
            }
        }
    },

    onProtocolFilterCorrespondentChange: function (field, newValue, oldValue, eOpts) {
        if (parseInt(Ext.getCmp('ProtocolFilterCorrespondent').getValue()) > 0) {
            Ext.getCmp('ProtocolFilterForm').applyFilter();
        } else if (!newValue) {
            Ext.getCmp('ProtocolFilterCorrespondent').setValue();
            Ext.getCmp('ProtocolFilterForm').applyFilter();
        } else if (oldValue) {
            if (newValue.length < 2 && newValue.length < oldValue.length) {
                Ext.getCmp('ProtocolFilterCorrespondent').setValue();
                Ext.getCmp('ProtocolFilterForm').applyFilter();
            }
        }
    },

    onArchiveFilterMetadataNumericStartChange11: function (field, newValue, oldValue, eOpts) {
        if (newValue && newValue > parseInt(Ext.getCmp('ProtocolFilterNumberEnd').getValue())) {
            field.setValue(oldValue);
        } else {
            Ext.getCmp('ProtocolFilterForm').applyFilter();
        }
    },

    onArchiveFilterMetadataNumericEndChange11: function (field, newValue, oldValue, eOpts) {
        if (newValue && newValue < parseInt(Ext.getCmp('ProtocolFilterNumberStart').getValue())) {
            field.setValue(oldValue);
        } else {
            Ext.getCmp('ProtocolFilterForm').applyFilter();
        }
    },

    onProtocolFilterDescriptionChange: function (field, newValue, oldValue, eOpts) {
        Ext.getCmp('ProtocolFilterForm').applyFilter();
    },

    onProtocolFilterDocumentChange: function (field, newValue, oldValue, eOpts) {
        Ext.getCmp('ProtocolFilterForm').applyFilter();
    },

    onProtocolFilterSiblingChange: function (field, newValue, oldValue, eOpts) {
        Ext.getCmp('ProtocolFilterForm').applyFilter();
    },

    onProtocolFilterSendMethodChange: function (field, newValue, oldValue, eOpts) {
        Ext.getCmp('ProtocolFilterForm').applyFilter();
    },

    onProtocolFilterNoteChange: function (field, newValue, oldValue, eOpts) {
        Ext.getCmp('ProtocolFilterForm').applyFilter();
    },

    onProtocolFilterActChange: function (field, newValue, oldValue, eOpts) {
        Ext.getCmp('ProtocolFilterForm').applyFilter();
    },

    onProtocolFilterDossierChange: function (field, newValue, oldValue, eOpts) {
        Ext.getCmp('ProtocolFilterForm').applyFilter();
    },

    onProtocolsGridItemContextMenu: function (dataview, record, item, index, e, eOpts) {

        if (record.get('reserved') && !mc2ui.app.settings.canManageReservedProtocol) {
            return;
        }

        e.stopEvent();
        var newX = e.xy[0];
        var newY = e.xy[1];
        var menu = Ext.getCmp('ProtocolProtocolEditMn');
        menu.showAt([newX, newY]);

        // Already canceled protocols cannot be canceled or modified
        if (record.get('canceled')) {
            menu.items.get('contextProtocolProtocolEdit').setDisabled(true);
            menu.items.get('contextProtocolProtocolCancel').setDisabled(true);
            menu.items.get('contextProtocolProtocolPrints').setDisabled(true);
        } else {
            menu.items.get('contextProtocolProtocolEdit').setDisabled(false);
            menu.items.get('contextProtocolProtocolCancel').setDisabled(false);
            menu.items.get('contextProtocolProtocolPrints').setDisabled(false);
        }

        Ext.getCmp('ProtocolProtocolEditMn').record = record;
    },

    onProtocolPnlBoxReady: function (component, width, height, eOpts) {
        Ext.getCmp('ProtocolFilterDateStart').setValue(new Date((new Date()).getFullYear(), 0, 1, 0, 0, 0, 0));
        Ext.getCmp('ProtocolFilterDateEnd').setValue(new Date((new Date()).getFullYear(), 11, 31, 23, 59, 59, 0));

        Ext.getStore('ProtocolSubjectKinds').load();
        Ext.getStore('ProtocolSendMethods').load();
        Ext.getStore('ProtocolTypesTree').load();
        Ext.getStore('ProtocolCorrespondents').load();
        Ext.getStore('CoreCities').load();
        Ext.getStore('Institutes').load();
        Ext.getStore('ProtocolMechanCodes').load();

        Ext.getStore('ProtocolSendMethodsFilter').load({
            callback: function (records, operation, success) {
                if (success) {
                    Ext.getCmp('ProtocolFilterSendMethod').select(0);
                }
            }
        });

        Ext.getCmp('ProtocolFilterDirection').select('A');
    }

});