from django.test import TestCase

from mc2.common.tests.factories import ContactFactory, CitiesFactory
from mc2.institute.tests.factories import InstituteFactory, InstituteRoleFactory
from mc2.institute.serializers import InstituteSerializer
from mc2.institute.models import Institute


class InstituteSerializerTest(TestCase):

    def test_serializer_contain_def_field(self):
        InstituteFactory(def_field=True)
        serializer = InstituteSerializer()
        print(serializer.data)
        self.assertIn('def', serializer.fields)

    def test_contact_values_right(self):

        contact = ContactFactory()
        institute = InstituteFactory(contact=contact)
        serializer = InstituteSerializer(instance=institute)
        data = serializer.data

        self.assertEqual(data['address'], contact.address)
        self.assertEqual(data['phone_num'], contact.phone_num)
        self.assertEqual(data['fax'], contact.fax)
        self.assertEqual(data['email'], contact.email)
        self.assertEqual(data['mobile'], contact.mobile)
        self.assertEqual(data['web'], contact.web)
        self.assertEqual(data['cap'], contact.cap)
        self.assertEqual(data['contact_id'], contact.contact_id)
        self.assertEqual(data['city_id'], contact.city.city_id)
        self.assertEqual(data['city_code'], contact.city.city_code)
        self.assertEqual(data['province'], contact.city.province)
        self.assertEqual(data['region'], contact.city.region.code)
        self.assertEqual(data['is_city'], contact.city.is_city)


    def test_serializer_contains_institute_values(self):
        institute_values = {
            "name": "Test institute",
            "mechan_code": "**********",
            "fiscal_code": "**********",
            "school_fiscal_code": "**********",
            "school_type": "123",
            "postal_account": **********,
            "job_director_id": 1,
            "job_vice_director_id": 1,
            "job_dsga_id": 1,
            "job_personnel_id": 1,
            "job_accounting_id": 1,
            "job_warehouse_id": 1,
            "job_registry_id": 1,
            "ipa_code": "**********",
            "ade_email": "<EMAIL>",
            "def_field": True
        }
        institute = InstituteFactory(**institute_values)
        serializer = InstituteSerializer(instance=institute)
        data = serializer.data

        for key, value in institute_values.items():
            self.assertEqual(data[key], value)

    def test_serializer_roles(self):
        institute = InstituteFactory()
        role = InstituteRoleFactory(institute=institute)
        serializer = InstituteSerializer(instance=institute)
        data = serializer.data
        self.assertIn('roles', data)
        self.assertIsInstance(data['roles'], list)
        self.assertEqual(len(data['roles']), 1)
        self.assertEqual(data['roles'][0]['id'], role.id)
        self.assertEqual(data['roles'][0]['institute_id'], institute.institute_id)
        self.assertEqual(data['roles'][0]['name'], role.name)
        self.assertEqual(data['roles'][0]['surname'], role.surname)
        self.assertEqual(data['roles'][0]['fiscal_code'], role.fiscal_code)
        self.assertEqual(data['roles'][0]['type'], role.type)

