from rest_framework import serializers

from .models import Institute, InstituteRole


class InstituteRoleSerializer(serializers.ModelSerializer):
    class Meta:
        model = InstituteRole
        fields = [
            "id",
            "institute_id",
            "name",
            "surname",
            "fiscal_code",
            "type"
        ]


class InstituteSerializer(serializers.ModelSerializer):
    address = serializers.CharField(source='contact.address',  read_only=True)
    phone_num = serializers.CharField(source='contact.phone_num', read_only=True)
    fax = serializers.CharField(source='contact.fax', read_only=True)
    email = serializers.CharField(source='contact.email', read_only=True)
    mobile = serializers.CharField(source='contact.mobile', read_only=True)
    web = serializers.CharField(source='contact.web', read_only=True)
    city_id = serializers.IntegerField(source='contact.city_id', read_only=True)
    cap = serializers.CharField(source='contact.cap', read_only=True)
    city_code = serializers.CharField(source='contact.city.city_code', read_only=True)
    province = serializers.CharField(source='contact.city.province', read_only=True)
    region = serializers.CharField(source='contact.city.region.code', read_only=True)
    is_city = serializers.IntegerField(source='contact.city.is_city', read_only=True)
    roles = serializers.SerializerMethodField()

    class Meta:
        model = Institute
        fields = [
            "institute_id",
            "name",
            "mechan_code",
            "school_fiscal_code",
            "fiscal_code",
            "school_type",
            "postal_account",
            "job_director_id",
            "job_vice_director_id",
            "job_dsga_id",
            "job_personnel_id",
            "job_accounting_id",
            "job_warehouse_id",
            "job_registry_id",
            "contact_id",
            "ipa_code",
            "address",
            "phone_num",
            "fax",
            "email",
            "mobile",
            "web",
            "city_id",
            "cap",
            "ade_email",
            "city_code",
            "province",
            "region",
            "is_city",
            "def_field",
            "roles"
        ]

    def get_roles(self, obj):
        return InstituteRoleSerializer(obj.instituterole_set.all(), many=True).data

    def to_representation(self, instance):
        data = super().to_representation(instance)
        data["def"] = data.pop("def_field")
        return data
